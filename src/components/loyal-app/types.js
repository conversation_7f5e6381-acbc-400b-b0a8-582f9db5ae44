export const InvoiceStatusType = [
  {
    value: 'paid',
    icon: 'mdi-check-circle-outline',
    color: 'green darken-2',
    text: 'common_confirmed',
  },
  {
    value: 'pending',
    icon: 'mdi-progress-clock',
    color: 'progress',
    text: 'common_waiting',
  },
  {
    value: 'cancelled',
    icon: 'mdi-close-circle-outline',
    color: 'error',
    text: 'common_canceled',
  },
];

export const InvoiceStatusFilterType = [
  {
    value: 'error',
    icon: 'mdi-close-octagon-outline',
    color: 'error',
    text: 'common_error',
  },
  {
    value: 'confirmed',
    icon: 'mdi-check-circle-outline',
    color: 'green darken-2',
    text: 'common_confirmed',
  },
  {
    value: 'rejected',
    icon: 'mdi-alert-outline',
    color: 'error',
    text: 'common_canceled',
  },
  {
    value: 'initiated',
    icon: 'mdi-cached',
    color: 'progress',
    text: 'common_processing',
  },
  {
    value: 'timeout',
    icon: 'mdi-clock-outline',
    color: 'error',
    text: 'common_timeout',
  },
  {
    value: 'waiting',
    icon: 'mdi-cached',
    color: 'progress',
    text: 'common_processing',
  },
  {
    value: 'refunded',
    icon: 'mdi-credit-card-refund-outline',
    color: 'warning',
    text: 'common_refund',
  },
];

export const SubscriptionStatusType = [
  {
    value: 'error',
    icon: 'mdi-close-octagon-outline',
    color: 'error',
    text: 'common_error',
  },
  {
    value: 'confirmed',
    icon: 'mdi-check-circle-outline',
    color: 'green darken-2',
    text: 'common_confirmed',
  },
  {
    value: 'rejected',
    icon: 'mdi-alert-outline',
    color: 'error',
    text: 'common_canceled',
  },
  {
    value: 'initiated',
    icon: 'mdi-cached',
    color: 'progress',
    text: 'common_processing',
  },
  {
    value: 'timeout',
    icon: 'mdi-clock-outline',
    color: 'error',
    text: 'common_timeout',
  },
  {
    value: 'waiting',
    icon: 'mdi-cached',
    color: 'progress',
    text: 'common_processing',
  },
  {
    value: 'refunded',
    icon: 'mdi-credit-card-refund-outline',
    color: 'warning',
    text: 'common_refund',
  },
];
