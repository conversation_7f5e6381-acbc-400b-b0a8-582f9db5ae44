<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
    card
  >
    <v-row>
      <v-col>
        <text-search v-model="search" />
      </v-col>
      <v-col>
        <multiselect
          ref="multiselect"
          v-model="status"
          :items="statusFilters"
          :label="$t('common_state')"
          prepend-icon="mdi-list-status"
          return-array
        />
      </v-col>
      <v-col>
        <date-select
          ref="dateSelect"
          @change="onDateRangeChange"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import debounce from 'lodash/debounce';
import TextSearch from '@components/common/filters/TextSearch.vue';
import Multiselect from '@components/common/filters/Multiselect.vue';
import DateSelect from '@components/common/page/selects/DateSelect.vue';
import { InvoiceStatusFilterType } from '@components/loyal-app/types';

export default {
  components: {
    TextSearch,
    Multiselect,
    DateSelect,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },
    statusOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      search: this.param.search ?? null,
      status: this.param.status ?? [],
      interval: this.param.interval,
    };
  },
  computed: {
    statusFilters() {
      return this.statusOptions.map(
        (item) => {
          const filterType = InvoiceStatusFilterType.find((status) => status.value === item);
          return {
            text: this.$t(filterType.text),
            value: filterType.value,
            icon: filterType.icon,
            color: filterType.color,
          };
        },
      );
    },
    internalParam() {
      return {
        search: this.search,
        status: this.status?.length ? this.status.join(',') : [],
        startDate: this.interval?.from,
        endDate: this.interval?.to,
        report: 'v2\\WlaInvoicesReport',
      };
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
  },
  beforeCreate() {
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 300);
  },
  mounted() {
    const startingPresetValue = this.$refs.dateSelect.getStartingPreset();
    const startingPreset = this.$refs.dateSelect.getPresetByValue(startingPresetValue);

    this.interval = {
      from: startingPreset.start,
      to: startingPreset.end,
    };

    this.$emit('update:param', this.internalParam);
  },
  methods: {
    onDateRangeChange(dateRange) {
      this.interval = dateRange;

      if (this.status.length > 0) {
        this.status = [];

        this.$emit('update:param', this.internalParam);
      }
    },
  },
};
</script>
