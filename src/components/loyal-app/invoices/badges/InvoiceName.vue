<template>
  <v-row
    align="center"
  >
    <v-tooltip
      bottom
    >
      <template #activator="{ on, attrs }">
        <v-icon
          :color="statusInfo.color"
          v-bind="attrs"
          v-on="on"
        >
          {{ statusInfo.icon }}
        </v-icon>
      </template>
      <span> {{ statusInfo.text }}</span>
    </v-tooltip>
    <span class="ml-1">
      {{ name }}
    </span>
  </v-row>
</template>

<script>

import { InvoiceStatusType } from '@components/loyal-app/types';

export default {
  props: {
    name: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      required: true,
    },
  },
  computed: {
    statusInfo() {
      const status = InvoiceStatusType.find((item) => item.value === this.status);
      if (status) {
        return {
          icon: status.icon,
          color: status.color,
          text: this.$t(status.text),
        };
      }

      return {
        icon: 'mdi-help-circle',
        color: 'grey',
        text: this.$t('invoice.status.unknown'),
      };
    },
  },
};
</script>
