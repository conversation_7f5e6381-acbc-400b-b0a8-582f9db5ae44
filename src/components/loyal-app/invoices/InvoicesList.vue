<template>
  <report-data-table
    ref="dataTable"
    :title="$t('common_invoices')"
    :headers="headers"
    url="/api/loyalapp/invoices"
    :filters="paramsInternal"
    :fetch-on-create="false"
    @data-fetched="onDataFetched"
  >
    <template #[`item.number`]="{ item }">
      <invoice-name
        :name="item.number"
        :status="item.status"
      />
    </template>

    <template #[`item.price`]="{ item }">
      <currency-formatter
        :value="item.price"
        :symbol="item.currency_symbol"
      />
    </template>

    <template #[`item.price_excluded_tax`]="{ item }">
      <currency-formatter
        :value="item.price_excluded_tax"
        :symbol="item.currency_symbol"
      />
    </template>

    <template #[`item.tax_amount`]="{ item }">
      <currency-formatter
        :value="item.tax_amount"
        :symbol="item.currency_symbol"
      />
    </template>

    <template #[`item.actions`]="{ item }">
      <v-row justify="end">
        <confirm-invoice-button
          :app="app"
          :invoice="item"
          @confirmed="fetchData()"
        />
        <download-invoice-button
          :app="app"
          :invoice="item"
        />
        <send-invoice-email-button
          :app="app"
          :invoice="item"
        />
      </v-row>
    </template>
  </report-data-table>
</template>

<script>
import ReportDataTable from '@/components/reports/ReportDataTable.vue';
import CurrencyFormatter from '@/components/common/formatters/CurrencyFormatter.vue';
import InvoiceName from '@/components/loyal-app/invoices/badges/InvoiceName.vue';
import ConfirmInvoiceButton from '@/components/loyal-app/invoices/buttons/ConfirmInvoiceButton.vue';
import DownloadInvoiceButton from '@/components/loyal-app/invoices/buttons/DownloadInvoiceButton.vue';
import SendInvoiceEmailButton from '@/components/loyal-app/invoices/buttons/SendInvoiceEmailButton.vue';

export default {
  components: {
    ReportDataTable,
    CurrencyFormatter,
    InvoiceName,
    ConfirmInvoiceButton,
    DownloadInvoiceButton,
    SendInvoiceEmailButton,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    paramsInternal() {
      return {
        ...this.params,
        app: this.app,
      };
    },
    headers() {
      return [
        {
          text: this.$t('common_username'),
          value: 'user_email',
          class: 'hidden-xs-only text-sm-start',
          showInRowExpand: false,
          sortable: false,
        },
        {
          text: this.$t('loyalApp_taxNumber'),
          value: 'client_tax_number',
          class: 'hidden-md-and-down md-and-up text-sm-center',
          showInRowExpand: true,
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('table.company_name'),
          value: 'client_name',
          class: 'hidden-md-and-down md-and-up text-sm-center',
          showInRowExpand: true,
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('loyalApp_issuanceDate'),
          value: 'issuance_date',
          class: 'hidden-sm-and-down md-and-up text-sm-center',
          showInRowExpand: true,
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('common_paymentDate'),
          value: 'payment_date',
          class: 'text-sm-center hidden-sm-and-down md-and-up',
          showInRowExpand: true,
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('loyalApp_invoiceNumber'),
          value: 'number',
          class: 'text-sm-start',
          showInRowExpand: false,
          sortable: false,
        },
        {
          text: this.$t('loyaltyCards_valueNet'),
          value: 'price',
          class: 'text-sm-center hidden-sm-and-down md-and-up',
          showInRowExpand: true,
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('loyaltyCards_valueGross'),
          value: 'price_excluded_tax',
          class: 'text-sm-center hidden-md-and-down md-and-up',
          showInRowExpand: true,
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('loyaltyCards_vatTax'),
          value: 'tax_amount',
          class: 'text-sm-center hidden-md-and-down md-and-up',
          showInRowExpand: true,
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('actions.actions'),
          value: 'actions',
          class: 'text-sm-end',
          sortable: false,
          showInRowExpand: false,
        },
      ];
    },
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
    onDataFetched(data) {
      this.$emit('data-fetched', data);
    },
  },
};

</script>
