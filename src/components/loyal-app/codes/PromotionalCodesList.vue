<template>
  <report-data-table
    ref="dataTable"
    :title="$t('loyalApp_promotionalCodes')"
    :headers="headers"
    url="/api/loyalapp/promotionalcodes"
    :filters="paramsInternal"
    :fetch-on-create="false"
    @data-fetched="onDataFetched"
  >
    <template #[`table-actions`]>
      <code-add-modal
        :app="app"
        @code-added="fetchData"
      />
    </template>

    <template #[`item.group_name`]="{ item }">
      {{ item.group_name }}
    </template>

    <template #[`item.promotional_code`]="{ item }">
      {{ item.promotional_code }}
    </template>

    <template #[`item.value`]="{ item }">
      {{ item.value }}
    </template>

    <template #[`item.ctime`]="{ item }">
      {{ item.ctime }}
    </template>

    <template #[`item.code_used`]="{ item }">
      <v-icon v-if="item.code_used" color="success">
        mdi-check-circle-outline
      </v-icon>
      <v-icon v-else color="error">
        mdi-close-circle-outline
      </v-icon>
    </template>
  </report-data-table>
</template>

<script>
import ReportDataTable from '@components/reports/ReportDataTable.vue';
import CodeAddModal from '@components/loyal-app/codes/CodeAddModal.vue';

export default {
  components: {
    ReportDataTable,
    CodeAddModal,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    headers() {
      return [
        {
          text: this.$t('loyalApp_groupName'),
          value: 'group_name',
          align: 'left',
          sortable: true,
        },
        {
          text: this.$t('loyalApp_promotionalCode'),
          value: 'promotional_code',
          align: 'center',
          sortable: true,
        },
        {
          text: this.$t('common_value'),
          value: 'value',
          align: 'center',
          sortable: true,
          class: 'hidden-md-and-down md-and-up',
        },
        {
          text: this.$t('common_createTime'),
          value: 'ctime',
          align: 'center',
          sortable: true,
          class: 'hidden-md-and-down md-and-up',
        },
        {
          text: this.$t('loyalApp_codeUsed'),
          value: 'code_used',
          align: 'center',
          sortable: true,
          class: 'hidden-xs-only',
        },
      ];
    },
    paramsInternal() {
      return {
        ...this.params,
        app: this.app,
      };
    },
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
    onDataFetched(data) {
      this.$emit('data-fetched', data);
    },
  },
};
</script>
