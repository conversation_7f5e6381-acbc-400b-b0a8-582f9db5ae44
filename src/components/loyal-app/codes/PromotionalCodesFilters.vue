<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
    card
  >
    <v-row>
      <v-col>
        <text-search v-model="search" />
      </v-col>
      <v-col>
        <multiselect
          ref="multiselect"
          v-model="codeUsed"
          :items="codeUsedOptions"
          :label="$t('loyalApp_codeUsed')"
          prepend-icon="mdi-check-circle-outline"
          unified
          allow-null
        />
      </v-col>
      <v-col>
        <date-select
          ref="dateSelect"
          @change="onDateRangeChange"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import debounce from 'lodash/debounce';
import TextSearch from '@components/common/filters/TextSearch.vue';
import Multiselect from '@components/common/filters/Multiselect.vue';
import DateSelect from '@components/common/page/selects/DateSelect.vue';

export default {
  components: {
    TextSearch,
    Multiselect,
    DateSelect,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },
    statusOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      search: this.param.search ?? null,
      codeUsed: this.param.codeUsed ?? null,
      interval: this.param.interval,
      codeUsedOptions: [
        {
          text: this.$t('common_all'),
          value: null,
        },
        {
          text: this.$t('loyalApp_codeUsed'),
          value: 1,
        },
        {
          text: this.$t('loyalApp_codeNotUsed'),
          value: 0,
        },
      ],
    };
  },
  computed: {
    internalParam() {
      return {
        search: this.search,
        codeUsed: this.codeUsed,
        startDate: this.interval?.from,
        endDate: this.interval?.to,
        report: 'v2\\WlaPromotionalCodesReport',
      };
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
  },
  beforeCreate() {
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 300);
  },
  mounted() {
    const startingPresetValue = this.$refs.dateSelect.getStartingPreset();
    const startingPreset = this.$refs.dateSelect.getPresetByValue(startingPresetValue);

    this.interval = {
      from: startingPreset.start,
      to: startingPreset.end,
    };

    this.$emit('update:param', this.internalParam);
  },
  methods: {
    onDateRangeChange(interval) {
      this.interval = interval;
    },
  },
};
</script>
