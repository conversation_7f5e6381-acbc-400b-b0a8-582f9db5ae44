<template>
  <v-dialog
    v-model="dialog"
    max-width="400px"
  >
    <v-card>
      <v-card-title class="title">
        <span class="headline">
          <h5 class="text-uppercase">{{ $t('loyalApp_addCode') }}</h5>
        </span>
      </v-card-title>
      <v-card-text>
        <v-container grid-list-md>
          <v-form
            ref="form"
            v-model="form.valid"
            lazy-validation
          >
            <v-row>
              <v-col
                cols="12"
              >
                <v-text-field
                  v-model="code.name"
                  prepend-icon="mdi-format-size"
                  :label="$t('name')"
                  required
                  :rules="form.validationRules.name"
                />
                <v-text-field
                  v-model="code.quantity"
                  prepend-icon="mdi-swap-vertical"
                  type="number"
                  :label="$t('loyalApp_quantity')"
                  :rules="form.validationRules.quantity"
                  required
                />
                <v-text-field
                  v-model="code.value"
                  prepend-icon="mdi-cash"
                  type="number"
                  :label="$t('common_value')"
                  required
                  :rules="form.validationRules.value"
                />
                <small>*{{ $t('common_fieldRequired') }}</small>
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </v-card-text>
      <v-card-actions>
        <v-spacer />
        <v-btn
          color="primary"
          text
          @click.native="closeDialog"
        >
          {{ $t('actions.return_to_list') }}
        </v-btn>
        <v-btn
          color="primary darken-1"
          :loading="loader"
          @click.native="submit"
        >
          {{ $t('actions.save') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';

export default {
  mixins: [
    SnackbarMixin,
  ],
  props: {
    app: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      dialog: false,
      loader: false,
      code: {
        name: '',
        quantity: '',
        value: '',
        type: 'DISPLOSABLE',
      },
      form: {
        validateOnBlur: true,
        valid: true,
        validationRules: {
          name: [
            (v) => !!v || this.$t('common_fieldRequired'),
            (v) => /^((?!%|\?|!|\)|\(|'|&).)*$/.test(v) || this.$t('loyalApp_invalidValue'),
            (v) => (v ? v.length <= 18 : false)
              || this.$t('loyalApp_infoValidNumberLength19'),
          ],
          quantity: [
            (v) => !!v || this.$t('common_fieldRequired'),
            (v) => (v > 0) || this.$t('loyalApp_infoPositiveNumberOnly'),
            (v) => /^[0-9]*$/.test(v) || this.$t('loyalApp_infoValidQuantity'),
            (v) => (v ? v.length <= 6 : false)
                || this.$t('loyalApp_infoValidNumberLength'),
          ],
          value: [
            (v) => !!v || this.$t('common_fieldRequired'),
            (v) => (v > 0) || this.$t('loyalApp_infoPositiveNumberOnly'),
            (v) => (v ? v.length <= 5 : false)
                || this.$t('loyalApp_infoValidNumberLength'),
          ],
        },
      },
    };
  },
  methods: {
    submit() {
      if (this.$refs.form.validate()) {
        this.loader = true;
        this.axios.post('/api/gateway/wla-admin/promotionalcodes', {
          name: this.code.name,
          quantity: this.code.quantity,
          value: this.code.value,
          type: this.code.type,
          app: this.app,
        })
          .then(
            (response) => {
              if (response.status === 200) {
                this.$emit('code-added');
                this.closeDialog();
              }
            },
            (error) => {
              if (error.request && error.request.status === 400) {
                this.showSnackbar(
                  'warning',
                  error,
                );
              } else {
                this.showSnackbar(
                  'warning',
                  error,
                );
              }
              // on error
              this.closeDialog();
            },
          );
      }
    },
    resetForm() {
      this.$refs.form.reset();
    },
    closeDialog() {
      this.form.valid = true;
      this.loader = false;
      this.form.validateOnBlur = true;
      this.dialog = false;
      this.resetForm();
    },
  },
};
</script>
