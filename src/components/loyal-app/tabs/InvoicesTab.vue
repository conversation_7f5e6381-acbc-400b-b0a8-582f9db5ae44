<template>
  <div>
    <invoices-filters
      v-model="filters"
      :status-options="statusOptions"
    />
    <invoices-list
      ref="dataTable"
      :app="app"
      :params="filters"
      @data-fetched="onDataFetched"
    />
  </div>
</template>

<script>
import InvoicesFilters from '@components/loyal-app/invoices/InvoicesFilters.vue';
import InvoicesList from '@components/loyal-app/invoices/InvoicesList.vue';

export default {
  components: {
    InvoicesFilters,
    InvoicesList,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      filters: {},
      statusOptions: [],
    };
  },
  watch: {
    app() {
      this.$nextTick(() => {
        this.fetchData();
      });
    },
  },

  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
    onDataFetched(data) {
      this.statusOptions = data.filters.statuses.map((item) => (item.name));
    },
  },
};

</script>
