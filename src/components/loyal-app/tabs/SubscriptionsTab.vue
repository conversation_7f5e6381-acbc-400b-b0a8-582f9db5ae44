<template>
  <div>
    <subscriptions-filters
      v-model="filters"
      :status-options="statusOptions"
    />
    <subscriptions-list
      ref="dataTable"
      :app="app"
      :params="filters"
      @data-fetched="onDataFetched"
    />
  </div>
</template>

<script>
import SubscriptionsFilters from '@components/loyal-app/subscriptions/SubscriptionsFilters.vue';
import SubscriptionsList from '@components/loyal-app/subscriptions/SubscriptionsList.vue';

export default {
  components: {
    SubscriptionsFilters,
    SubscriptionsList,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      filters: {},
      statusOptions: [],
    };
  },
  watch: {
    app() {
      this.$nextTick(() => {
        this.fetchData();
      });
    },
  },

  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
    onDataFetched(data) {
      this.statusOptions = data.filters.statuses.map((item) => (item.name));
    },
  },
};

</script>
