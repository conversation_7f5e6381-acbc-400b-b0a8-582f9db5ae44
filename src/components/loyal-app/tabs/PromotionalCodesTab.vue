<template>
  <div>
    <promotional-codes-filters
      v-model="filters"
      :status-options="statusOptions"
    />
    <promotional-codes-list
      ref="dataTable"
      :app="app"
      :params="filters"
      @data-fetched="onDataFetched"
    />
  </div>
</template>

<script>
import PromotionalCodesFilters from '@components/loyal-app/codes/PromotionalCodesFilters.vue';
import PromotionalCodesList from '@components/loyal-app/codes/PromotionalCodesList.vue';

export default {
  components: {
    PromotionalCodesFilters,
    PromotionalCodesList,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      filters: {},
      statusOptions: [],
    };
  },
  watch: {
    app() {
      this.$nextTick(() => {
        this.fetchData();
      });
    },
  },

  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
    onDataFetched(data) {
      // Jeśli API zwraca opcje statusów, można je tutaj ustawić
      // this.statusOptions = data.filters.statuses.map((item) => (item.name));
    },
  },
};

</script>
