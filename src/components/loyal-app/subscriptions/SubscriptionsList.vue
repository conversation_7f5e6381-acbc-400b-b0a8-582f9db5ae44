<template>
  <report-data-table
    ref="dataTable"
    :title="$t('loyalApp_subscriptions')"
    :headers="headers"
    url="/api/loyalapp/subscription-packages"
    :filters="paramsInternal"
    :fetch-on-create="false"
    @data-fetched="onDataFetched"
  >
    <template #[`item.status`]="{ item }">
      <subscription-status-badge :status="item.status" />
    </template>

    <template #[`item.value`]="{ item }">
      <currency-formatter
        :value="item.value"
        :symbol="item.currencySymbol"
      />
    </template>

    <template #[`item.left`]="{ item }">
      <currency-formatter
        :value="item.left"
        :symbol="item.currencySymbol"
      />
    </template>
  </report-data-table>
</template>

<script>
import ReportDataTable from '@/components/reports/ReportDataTable.vue';
import CurrencyFormatter from '@/components/common/formatters/CurrencyFormatter.vue';
import SubscriptionStatusBadge from '@/components/loyal-app/subscriptions/badges/SubscriptionStatusBadge.vue';

export default {
  components: {
    ReportDataTable,
    CurrencyFormatter,
    SubscriptionStatusBadge,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    paramsInternal() {
      return {
        ...this.params,
        app: this.app,
      };
    },
    headers() {
      return [
        {
          text: this.$t('common_id'),
          value: 'id',
          class: 'text-sm-start',
          showInRowExpand: false,
          sortable: false,
        },
        {
          text: this.$t('loyalApp_user'),
          value: 'userEmail',
          class: 'text-sm-start',
          showInRowExpand: false,
          sortable: false,
        },
        {
          text: this.$t('loyalApp_fleet'),
          value: 'fleetEmail',
          class: 'text-sm-start',
          showInRowExpand: false,
          sortable: false,
        },
        {
          text: this.$t('loyaltyApp_startTime'),
          value: 'startTime',
          class: 'text-sm-start',
          showInRowExpand: false,
          sortable: false,
        },
        {
          text: this.$t('loyaltyApp_endTime'),
          value: 'endTime',
          class: 'text-sm-start',
          showInRowExpand: false,
          sortable: false,
        },
        {
          text: this.$t('common_state'),
          value: 'status',
          class: 'text-sm-center',
          align: 'center',
          showInRowExpand: false,
          sortable: false,
        },
        {
          text: this.$t('loyalApp_value'),
          value: 'value',
          class: 'text-sm-end',
          align: 'right',
          showInRowExpand: true,
          sortable: false,
        },
        {
          text: this.$t('common_left'),
          value: 'left',
          class: 'text-sm-end',
          align: 'right',
          showInRowExpand: true,
          sortable: false,
        },
      ];
    },
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
    onDataFetched(data) {
      this.$emit('data-fetched', data);
    },
  },
};

</script>
