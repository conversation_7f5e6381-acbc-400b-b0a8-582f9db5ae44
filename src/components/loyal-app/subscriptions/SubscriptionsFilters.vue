<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
    card
  >
    <v-row>
      <v-col>
        <text-search v-model="search" />
      </v-col>
      <v-col>
        <multiselect
          ref="multiselect"
          v-model="status"
          :items="statusFilters"
          :label="$t('common_state')"
          prepend-icon="mdi-list-status"
          return-array
        />
      </v-col>
      <v-col />
    </v-row>
  </v-container>
</template>

<script>
import debounce from 'lodash/debounce';
import TextSearch from '@components/common/filters/TextSearch.vue';
import Multiselect from '@components/common/filters/Multiselect.vue';
import { SubscriptionStatusType } from '@components/loyal-app/types';

export default {
  components: {
    TextSearch,
    Multiselect,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },
    statusOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      search: this.param.search ?? null,
      status: this.param.status ?? [],
    };
  },
  computed: {
    statusFilters() {
      return this.statusOptions.map(
        (item) => {
          const filterType = SubscriptionStatusType.find((status) => status.value === item);
          return {
            text: this.$t(filterType.text),
            value: filterType.value,
            icon: filterType.icon,
            color: filterType.color,
          };
        },
      );
    },
    internalParam() {
      return {
        search: this.search,
        status: this.status?.length ? this.status.join(',') : [],
        report: 'v2\\WlaSubscriptionPackagesReport',
      };
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
  },
  beforeCreate() {
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 300);
  },
  mounted() {
    const startingPresetValue = this.$refs.dateSelect.getStartingPreset();
    const startingPreset = this.$refs.dateSelect.getPresetByValue(startingPresetValue);

    this.interval = {
      from: startingPreset.start,
      to: startingPreset.end,
    };

    this.$emit('update:param', this.internalParam);
  },
  methods: {

  },
};
</script>
