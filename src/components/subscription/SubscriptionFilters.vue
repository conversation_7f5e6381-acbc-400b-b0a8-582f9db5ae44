<template>
  <div>
    <v-row>
      <v-col>
        <text-search
          v-model="search"
        />
      </v-col>
      <v-col>
        <multiselect2
          v-model="statuses"
          :items="allowedStatuses"
          :label="$t('common_state')"
          prepend-icon="mdi-cash-check"
          unified
          allow-null
        />
      </v-col>
      <v-col>
        <date-select
          :show-custom="false"
          @change="onDateRangeChange"
        />
      </v-col>
    </v-row>
  </div>
</template>
<script>
import Multiselect2 from '@components/common/page/selects/Multiselect2.vue';
import TextSearch from '@components/common/filters/TextSearch.vue';
import DateSelect from '@components/common/page/selects/DateSelect.vue';
import debounce from 'lodash/debounce';
import { StatusesType } from '@components/subscription/types';

export default {
  components: {
    DateSelect,
    TextSearch,
    Multiselect2,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      search: this.param.search ?? null,
      interval: this.param.interval ?? null,
      statusOptions: null,
      statuses: [],
    };
  },
  computed: {
    // scalony obiekt filtrów do przekazania
    internalParam() {
      return {
        status: this.statuses.length ? this.statuses.join(',') : '',
        search: this.search,
        dateFrom: this.dateFrom,
        dateTo: this.dateTo,
        interval: this.interval,
      };
    },
    allowedStatuses() {
      return Object.entries(StatusesType).map(([key, { text }]) => ({
        text: this.$t(text),
        value: key,
      }));
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
  },
  beforeCreate() {
    // jeden debounced watcher na cały internalParam
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 600);
  },
  methods: {
    onDateRangeChange(dateRange) {
      this.interval = dateRange.value;
      this.dateFrom = dateRange.from;
      this.dateTo = dateRange.to;
    },
  },
};
</script>
