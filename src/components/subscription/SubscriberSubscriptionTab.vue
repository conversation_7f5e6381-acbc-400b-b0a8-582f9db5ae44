<template>
  <v-container
    grid-list-md
    fluid
  >
    <subscription-list
      ref="list"
      :url="`administration/subscriber/${subscriberId}/subscriptions`"
    >
      <template #table-actions>
        <subscription-add-modal
          ref="addSubscriptionModal"
          :subscriber-id="subscriberId"
          :on-success="refresh"
        />
        <subscription-add-dealer-modal
          ref="addSubscriptionModal"
          :subscriber-id="subscriberId"
          :on-success="refresh"
        />
      </template>
    </subscription-list>
  </v-container>
</template>

<script>
import SubscriptionList from '@components/subscription/SubscriptionList.vue';
import SubscriptionAddModal from './modals/SubscriptionAddModal.vue';
import SubscriptionAddDealerModal from './modals/SubscriptionAddDealerModal.vue';

export default {
  components: {
    SubscriptionList,
    SubscriptionAddModal,
    SubscriptionAddDealerModal,
  },
  props: {
    subscriberId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      data: [],
      loading: false,
      headers: [
        {
          text: this.$t('common_startDate'),
          value: 'startDate',
          width: '100',
          sortable: false,
        },
        {
          text: this.$t('common_subscriptionsEnddate'),
          value: 'endDate',
          width: '100',
          sortable: false,
        },
        {
          text: this.$t('common_state'),
          value: 'status',
          width: '150',
          sortable: false,
        },
        {
          text: this.$t('common_price'),
          value: 'grossValue',
          class: 'text-sm-end',
          width: '120',
          sortable: false,
        },
        {
          text: this.$t('admin_vat'),
          value: 'vatTax',
          class: 'text-sm-end',
          width: '80',
          sortable: false,
        },
        {
          text: this.$t('common_type'),
          value: 'type',
          width: '100',
          sortable: false,
        },
        {
          text: this.$t('admin_added'),
          value: 'added',
          width: '200',
          sortable: false,
        },
        {
          text: this.$t('admin_whoAdded'),
          value: 'whoAddedEmail',
          width: '200',
          sortable: false,
        },
        {
          text: this.$t('common_comment'),
          value: 'comment',
          sortable: false,
        },
        {
          text: this.$t('actions.actions'),
          class: 'text-sm-end',
          sortable: false,
          width: '150',
        },
      ],
      deleteDialog: false,
    };
  },
  methods: {
    refresh() {
      this.$refs.list.$refs.table.fetchData();
    },
  },
};
</script>
