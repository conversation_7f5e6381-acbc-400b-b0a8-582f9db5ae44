<template>
  <div>
    <v-btn
      class="ml-2"
      color="primary"
      x-small
      fab
      elevation="1"
      @click.stop
      @click.native="dialog = true"
    >
      <v-icon>
        mdi-plus
      </v-icon>
    </v-btn>
    <v-layout
      row
      justify-center
    >
      <v-dialog
        v-model="dialog"
        scrollable
        persisten
        content-class="dialogWidth-3"
        class="modal"
      >
        <v-card>
          <v-card-title class="title">
            <span class="headline">
              <h5 class="text-uppercase">
                {{ $t('admin_add') }}
              </h5>
            </span>
          </v-card-title>
          <v-progress-linear
            v-if="loaders.site"
            :indeterminate="true"
            class="mt-0"
          />
          <v-card-text class="pt-6">
            <div class="text-center">
              <v-progress-circular
                v-if="loaders.site"
                class="circleProgress"
                :size="90"
                :width="7"
                color="primary"
                indeterminate
              />
            </div>
            <template v-if="!loaders.site">
              <v-container grid-list-md>
                <multiselect2
                  v-model="subModel"
                  :items="subscriptionOptions"
                  :label="$t('common_subscription')"
                  prepend-icon="mdi-list-status"
                  unified
                  single
                  allow-null
                />
                <subscription-calculation
                  :currency-sym="currencySym"
                  :subscription-calculation="subscriptionCalculation"
                />
              </v-container>
            </template>
          </v-card-text>
          <v-card-actions v-if="!loaders.site">
            <v-spacer />
            <v-btn
              color="gray"
              text
              @click.native="closeDialog"
            >
              {{ $t('actions.return_to_list') }}
            </v-btn>
            <v-btn
              color="secondary"
              :loading="loaders.actualize"
              @click.native="save"
            >
              {{ $t('admin_save') }}
            </v-btn>
            <v-btn
              color="primary darken-1"
              :loading="loaders.actualize"
              @click.native="add"
            >
              {{ $t('admin_add') }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-layout>
  </div>
</template>

<script>

import SubscriptionCalculation from '@components/subscription/SubscriptionCalculation.vue';
import Multiselect2 from '@components/common/page/selects/Multiselect2.vue';

export default {
  components: {
    Multiselect2,
    SubscriptionCalculation,
  },
  props: {
    onSuccess: {
      type: Function,
      default: () => {},
    },
    discountPercent: {
      type: Number,
      default: 0,
    },
    subscriberId: {
      type: Number,
      required: false,
      default: null,
    },
    dealerId: {
      type: Number,
      required: false,
      default: null,
    },
  },
  data() {
    return {
      subscriptionOptions: null,
      discount: this.discountPercent,
      loaders: {
        site: true,
        actualize: true,
      },
      currencySym: 'zł',
      subscriptionCalculation: {
      },
      subModel: null,
      dialog: false,
    };
  },
  watch: {
    subModel(val) {
      this.getCalculateSubscription(val);
    },
    dialog(val) {
      // get data only when dialog shows up
      if (val) {
        this.loaders.actualize = false;
        this.reset();
        this.getSubscriptionsList();
      }
    },
  },
  methods: {
    reset() {
      this.loaders.site = false;
      this.loaders.actualize = false;
      this.subscriptionCalculation = {};
    },
    onError() {
      // on error
      this.closeDialog();
    },
    save() {
      if (this.subModel === null) {
        return;
      }
      this.loaders.site = true;
      this.loaders.actualize = true;

      this.axios.post(
        `/administration/subscriber/${this.$route.params.id}/subscriptions/package/${this.subModel}/save`,
      )
        .then(
          (response) => {
            if (response.status === 200) {
              this.onSuccess();
              this.closeDialog();
            }
          },
          () => {
            // on error
            this.loaders.site = false;
            this.loaders.actualize = false;
          },
        );
    },
    add() {
      if (this.subModel === null) {
        return;
      }
      this.loaders.site = true;
      this.loaders.actualize = true;

      this.axios.post(
        `/administration/subscriber/${this.$route.params.id}/subscriptions/package/${this.subModel}/add`,
      )
        .then(
          (response) => {
            if (response.status === 200) {
              this.onSuccess();
              this.closeDialog();
            }
          },
          () => {
            // on error
            this.loaders.site = false;
            this.loaders.actualize = false;
          },
        );
    },
    getSubscriptionsList() {
      // this.loaders.site = true;
      this.axios.get(
        `/administration/subscriber/${this.$route.params.id}/subscriptions/plans`,
      )
        .then((response) => {
          this.subscriptionOptions = response.data.map((item) => ({
            value: item.id,
            text: `${item.code} - ${item.monthsLength} months - ${item.value} ${item.currencySymbol}`,
          }));
          // this.loaders.site = false;
        })
        .catch(() => {
          // this.showSnackbar(
          //   'error',
          //   this.$t('common_errorHeader'),
          // );
        });
    },
    getCalculateSubscription(packageId) {
      // this.loaders.site = true;
      this.axios.get(
        `/administration/subscriber/${this.$route.params.id}/subscriptions/package/${packageId}/calculate`,
      )
        .then((response) => {
          this.subscriptionCalculation = response.data;
          // this.loaders.site = false;
        })
        .catch(() => {
          // this.showSnackbar(
          //   'error',
          //   this.$t('common_errorHeader'),
          // );
        });
    },
    closeDialog() {
      this.reset();
      this.dialog = false;
    },
  },
};
</script>
