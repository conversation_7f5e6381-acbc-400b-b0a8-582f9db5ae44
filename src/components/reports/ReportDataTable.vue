<template>
  <div>
    <v-row align="center">
      <!-- 1) Pierwsza kolumna: tytuł, przyklejona do lewej -->
      <v-col cols="auto">
        <h2 v-if="title">
          {{ title }}
        </h2>
      </v-col>

      <!-- 2) Spacer wypychający kolejne dwie kolumny na prawą stronę -->
      <v-spacer />

      <!-- 4) Trzecia kolumna: akcje, przyklejone do prawej -->
      <v-col
        cols="auto"
        class="d-flex justify-end"
      >
        <slot name="table-actions" />
        <report-create-modal :params="params" />
        <btn-refresh
          class="ml-2"
          @click="fetchData"
        />
      </v-col>
    </v-row>

    <v-data-table
      mobile-breakpoint="0"
      :headers="computedHeaders"
      :items="items"
      :loading="loading"
      :options.sync="options"
      :server-items-length="totalItems"
      :items-per-page="options.itemsPerPage"
      :hide-default-footer="loading"
      :footer-props="footerProps"
    >
      <template #progress>
        <div class="text-center mx-n4">
          <v-progress-linear
            class="loader"
            indeterminate
            color="primary"
          />
        </div>
      </template>
      <template #item="slotProps">
        <tr>
          <td
            v-for="header in computedHeaders"
            :key="header.value"
            :class="getAlignClass(header.align)"
          >
            <slot
              :name="`item.${header.value}`"
              v-bind="slotProps"
            >
              {{ slotProps.item[header.value] }}
            </slot>
          </td>
        </tr>
      </template>
    </v-data-table>
  </div>
</template>

<script>
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';

export default {
  components: { ReportCreateModal, BtnRefresh },
  mixins: [
    SnackbarMixin,
  ],
  props: {
    title: {
      type: String,
      required: false,
      default: null,
    },
    url: {
      type: String,
      required: true,
    },
    headers: {
      type: Array,
      default: () => [], // jeśli puste, generujemy automatycznie
    },
    filters: {
      type: Object,
      default: () => {}, // jeśli puste, generujemy automatycznie
    },
    fetchOnCreate: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      items: [],
      totalItems: 0,
      loading: false,
      autoHeaders: [],
      footerProps: {
        'items-per-page-options': [25, 50, 100],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      options: {
        page: 1,
        itemsPerPage: 25,
        sortBy: [],
        sortDesc: [],
      },
    };
  },
  computed: {
    computedHeaders() {
      return this.headers.length > 0 ? this.headers : this.autoHeaders;
    },
    params() {
      return {
        page: this.options.page,
        perPage: this.options.itemsPerPage,
        ...this.filters,
      };
    },
  },
  watch: {
    filters: {
      handler() {
        this.options.page = 1;
        this.fetchData();
      },
      deep: true,
    },
    options: {
      handler() {
        this.fetchData();
      },
      deep: true,
    },
  },
  created() {
    if (this.fetchOnCreate) {
      this.fetchData();
    }
  },
  methods: {
    getAlignClass(align) {
      switch (align) {
        case 'center':
          return 'text-center';
        case 'right':
          return 'text-right';
        case 'left':
        default:
          return 'text-left';
      }
    },
    async fetchData() {
      if (this.loading) {
        return;
      }
      this.loading = true;
      this.items = [];
      try {
        const response = await this.axios.get(this.url, { params: this.params });
        const { data, total } = response.data;

        if (data.length > 0 && this.headers.length === 0) {
          this.autoHeaders = Object.keys(data[0]).map((key) => ({
            text: this.formatHeader(key),
            value: key,
          }));
        }

        this.items = data;
        this.totalItems = total;

        this.$emit('data-fetched', response.data);
      } catch (e) {
        this.showSnackbar('error', this.$t('common_error_occurred'));
      } finally {
        this.loading = false;
      }
    },
    formatHeader(key) {
      return key
        .replace(/_/g, ' ')
        .replace(
          /\b\w/g,
          (l) => l.toUpperCase(),
        );
    },
  },
};
</script>
