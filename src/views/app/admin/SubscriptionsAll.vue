<template>
  <v-container fluid>
    <v-card>
      <v-toolbar
        color="secondary"
        dark
        flat
      >
        <v-btn
          icon
          dark
        >
          <v-icon>mdi-account-multiple</v-icon>
        </v-btn>

        <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
          {{ $t('admin_subscriptionListHeading') }}
        </v-toolbar-title>
        <v-spacer />
      </v-toolbar>
      <v-card-text>
        <all-subscription-tab />
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
import AllSubscriptionTab from '@components/subscription/AllSubscriptionTab.vue';

export default {
  name: 'SubscriptionsAllView',
  components: {
    AllSubscriptionTab,
  },
};
</script>
